-- Shared Audit Functions
-- Contains audit trigger functions used by multiple tables
-- Vendor Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_vendor_changes" () R<PERSON>UR<PERSON> "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, old_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.vendor_id, OLD.name, OLD.description, OLD.org_id, OLD.client_id, OLD.project_id,
            OLD.contact_name, OLD.contact_email, OLD.contact_phone, OLD.contact_address, OLD.website,
            OLD.vendor_type, OLD.tax_id, OLD.payment_terms, OLD.payment_terms_days, OLD.credit_limit,
            OLD.currency, OLD.is_active, OLD.certification_info, OLD.insurance_info, OLD.additional_data,
            OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.vendor_id, NEW.name, NEW.description, NEW.org_id, NEW.client_id, NEW.project_id,
            NEW.contact_name, NEW.contact_email, NEW.contact_phone, NEW.contact_address, NEW.website,
            NEW.vendor_type, NEW.tax_id, NEW.payment_terms, NEW.payment_terms_days, NEW.credit_limit,
            NEW.currency, NEW.is_active, NEW.certification_info, NEW.insurance_info, NEW.additional_data,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.vendor_audit (
            operation_type, changed_by, changed_at, new_values,
            vendor_id, name, description, org_id, client_id, project_id,
            contact_name, contact_email, contact_phone, contact_address, website,
            vendor_type, tax_id, payment_terms, payment_terms_days, credit_limit,
            currency, is_active, certification_info, insurance_info, additional_data,
            created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.vendor_id, NEW.name, NEW.description, NEW.org_id, NEW.client_id, NEW.project_id,
            NEW.contact_name, NEW.contact_email, NEW.contact_phone, NEW.contact_address, NEW.website,
            NEW.vendor_type, NEW.tax_id, NEW.payment_terms, NEW.payment_terms_days, NEW.credit_limit,
            NEW.currency, NEW.is_active, NEW.certification_info, NEW.insurance_info, NEW.additional_data,
            NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_vendor_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_vendor_changes" () IS 'Audit trigger function for vendor table';

-- Purchase Order Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_purchase_order_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, old_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.purchase_order_id, OLD.po_number, OLD.description, OLD.po_date, OLD.project_id, OLD.vendor_id,
            OLD.account, OLD.original_amount, OLD.co_amount, OLD.freight, OLD.tax, OLD.other,
            OLD.notes, OLD.created_by_user_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.purchase_order_id, NEW.po_number, NEW.description, NEW.po_date, NEW.project_id, NEW.vendor_id,
            NEW.account, NEW.original_amount, NEW.co_amount, NEW.freight, NEW.tax, NEW.other,
            NEW.notes, NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.purchase_order_audit (
            operation_type, changed_by, changed_at, new_values,
            purchase_order_id, po_number, description, po_date, project_id, vendor_id,
            account, original_amount, co_amount, freight, tax, other,
            notes, created_by_user_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.purchase_order_id, NEW.po_number, NEW.description, NEW.po_date, NEW.project_id, NEW.vendor_id,
            NEW.account, NEW.original_amount, NEW.co_amount, NEW.freight, NEW.tax, NEW.other,
            NEW.notes, NEW.created_by_user_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_purchase_order_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_purchase_order_changes" () IS 'Audit trigger function for purchase_order table';
<<<<<<< HEAD

-- Work Package Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_work_package_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '********-0000-0000-0000-************'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values,
            work_package_id, name, description, project_id, parent_work_package_id,
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.work_package_id, OLD.name, OLD.description, OLD.project_id, OLD.parent_work_package_id,
            OLD.purchase_order_id, OLD.wbs_library_item_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            work_package_id, name, description, project_id, parent_work_package_id,
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id, NEW.parent_work_package_id,
            NEW.purchase_order_id, NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, new_values,
            work_package_id, name, description, project_id, parent_work_package_id,
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id, NEW.parent_work_package_id,
            NEW.purchase_order_id, NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_work_package_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_work_package_changes" () IS 'Audit trigger function for work_package table';
=======
>>>>>>> develop
